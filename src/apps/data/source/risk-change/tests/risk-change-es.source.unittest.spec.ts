import { Test, TestingModule } from '@nestjs/testing';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { CompanySearchService } from '../../../company/company-search.service';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { RiskChangeEsSource } from '../risk-change-es.source';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('RiskChangeEsSource 单元测试', () => {
  let service: RiskChangeEsSource;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockDimensionHitDetailProcessor: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRelatedDimensionHitDetailsProcessor: jest.Mocked<RelatedDimensionHitDetailProcessor>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockEsClient: jest.Mocked<Client>;

  const [testOrgId, testUserId] = generateUniqueTestIds('risk-change-es.source.unittest.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeEach(async () => {
    // 创建 mock 对象
    mockConfigService = {
      esConfig: {
        riskChangeList: {
          nodes: ['http://localhost:9200'],
          indexName: 'test-risk-change-index',
        },
      },
    } as any;

    mockRiskChangeHelper = {
      hitMainInfoUpdateCapitalChange: jest.fn(),
    } as any;

    mockCompanySearchService = {
      searchCompany: jest.fn(),
    } as any;

    mockDimensionHitDetailProcessor = {
      fetchHits: jest.fn(),
      bindRiskChangeEsSearchFn: jest.fn(),
    } as any;

    mockRelatedDimensionHitDetailsProcessor = {
      processAnalyze: jest.fn(),
      fetchHits: jest.fn(),
      bindRiskChangeEsSearchFn: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      checkCaseTypeField: jest.fn(),
    } as any;

    mockEsClient = {
      search: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RiskChangeEsSource,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockDimensionHitDetailProcessor },
        { provide: RelatedDimensionHitDetailProcessor, useValue: mockRelatedDimensionHitDetailsProcessor },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    service = module.get<RiskChangeEsSource>(RiskChangeEsSource);
    
    // 手动设置 ES 客户端，因为它在构造函数中创建
    (service as any).esClient = mockEsClient;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('analyze 方法测试', () => {
    it('应该正确处理空的维度策略列表', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const dimensionHitStrategyPOs: DimensionHitStrategyPO[] = [];

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(result).toEqual([]);
    });

    it('应该正确分离关联方和非关联方维度', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const relatedDimension = new DimensionHitStrategyPO();
      relatedDimension.key = DimensionTypeEnums.ActualControllerRiskChange;
      
      const normalDimension = new DimensionHitStrategyPO();
      normalDimension.key = DimensionTypeEnums.RiskChange;

      const dimensionHitStrategyPOs = [relatedDimension, normalDimension];

      // Mock 关联方处理器返回结果
      const mockRelatedResult: DimensionHitResultPO[] = [{
        key: DimensionTypeEnums.ActualControllerRiskChange,
        isHit: true,
        totalHits: 1,
        description: '测试关联方结果',
      }];
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue(mockRelatedResult);

      // Mock 父类 analyze 方法的行为
      jest.spyOn(service as any, 'analyzeByAggs').mockResolvedValue([{
        key: DimensionTypeEnums.RiskChange,
        isHit: true,
        totalHits: 1,
        description: '测试普通维度结果',
      }]);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.processAnalyze).toHaveBeenCalledWith(
        [relatedDimension],
        companyId
      );
      expect(result).toHaveLength(2);
      expect(result[0].key).toBe(DimensionTypeEnums.ActualControllerRiskChange);
      expect(result[1].key).toBe(DimensionTypeEnums.RiskChange);
    });

    it('应该只处理关联方维度当没有普通维度时', async () => {
      // Arrange
      const companyId = 'test-company-id';
      const relatedDimension = new DimensionHitStrategyPO();
      relatedDimension.key = DimensionTypeEnums.RecentInvestCancellationsRiskChange;
      
      const dimensionHitStrategyPOs = [relatedDimension];

      const mockRelatedResult: DimensionHitResultPO[] = [{
        key: DimensionTypeEnums.RecentInvestCancellationsRiskChange,
        isHit: true,
        totalHits: 2,
        description: '测试投资注销结果',
      }];
      mockRelatedDimensionHitDetailsProcessor.processAnalyze.mockResolvedValue(mockRelatedResult);

      // Act
      const result = await service.analyze(companyId, dimensionHitStrategyPOs);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.processAnalyze).toHaveBeenCalledWith(
        [relatedDimension],
        companyId
      );
      expect(result).toEqual(mockRelatedResult);
    });
  });

  describe('getDimensionDetail 方法测试', () => {
    it('应该正确处理关联方维度详情查询', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.ListedEntityRiskChange;
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      const mockResponse = new HitDetailsBaseResponse();
      mockResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 5,
      };
      mockResponse.Result = [{ id: 'test-result' }];

      mockRelatedDimensionHitDetailsProcessor.fetchHits.mockResolvedValue(mockResponse);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockRelatedDimensionHitDetailsProcessor.fetchHits).toHaveBeenCalledWith(dimension, params);
      expect(result).toEqual(mockResponse);
    });

    it('应该正确处理 RiskChange 维度详情查询', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 父类方法返回的基础数据
      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [
        { id: 'item1', Category: 1 },
        { id: 'item2', Category: 2 },
      ];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 2,
      };

      jest.spyOn(service as any, 'getDetailFromEs').mockResolvedValue(mockBaseResponse);

      // Mock 详情处理器返回的过滤结果
      const mockFilteredHits = [{ id: 'item1', Category: 1 }];
      mockDimensionHitDetailProcessor.fetchHits.mockResolvedValue(mockFilteredHits);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockDimensionHitDetailProcessor.fetchHits).toHaveBeenCalledWith(
        mockBaseResponse,
        dimension,
        params
      );
      expect(result.Result).toEqual(mockFilteredHits);
      expect(result.Paging.TotalRecords).toBe(1); // 过滤后的数量
    });

    it('应该正确处理 MainInfoUpdateCapitalChange 维度详情查询', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.MainInfoUpdateCapitalChange;
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 父类方法返回的基础数据
      const mockBaseResponse = new HitDetailsBaseResponse();
      mockBaseResponse.Result = [
        { id: 'item1', Category: 37 },
        { id: 'item2', Category: 37 },
        { id: 'item3', Category: 37 },
      ];
      mockBaseResponse.Paging = {
        PageSize: 10,
        PageIndex: 1,
        TotalRecords: 3,
      };

      jest.spyOn(service as any, 'getDetailFromEs').mockResolvedValue(mockBaseResponse);

      // Mock helper 方法返回命中结果
      mockRiskChangeHelper.hitMainInfoUpdateCapitalChange.mockReturnValue([
        { id: 'item1', Category: 37 },
        { id: 'item3', Category: 37 },
      ]);

      // Act
      const result = await service.getDimensionDetail(dimension, params);

      // Assert
      expect(mockRiskChangeHelper.hitMainInfoUpdateCapitalChange).toHaveBeenCalledWith(
        mockBaseResponse.Result,
        dimension
      );
      expect(result.Result).toHaveLength(2);
      expect(result.Paging.TotalRecords).toBe(2);
    });
  });
});
