import { Test, TestingModule } from '@nestjs/testing';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { PersonHelper } from '../../helper/person.helper';
import { CompanyDetailService } from '../../../company/company-detail.service';
import { CompanySearchService } from '../../../company/company-search.service';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('RelatedDimensionHitDetailProcessor 单元测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  const [testOrgId, testUserId] = generateUniqueTestIds('related-dimension-hit-detail.processor.unittest.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getPersonData: jest.fn(),
      getActualControllerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getCompanyDetail: jest.fn(),
    } as any;

    mockCompanySearchService = {
      getInvestCompanyList: jest.fn(),
      getListedEntityList: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      fetchHits: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      hitMainInfoUpdateCapitalChange: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      checkCaseTypeField: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processAnalyze 方法测试', () => {
    it('应该正确处理空的关联方维度列表', async () => {
      // Arrange
      const relatedRiskChangeDims: DimensionHitStrategyPO[] = [];
      const companyId = 'test-company-id';

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toEqual([]);
    });

    it('应该正确处理实际控制人风险变更维度', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.ActualControllerRiskChange;
      dimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      }];

      const relatedRiskChangeDims = [dimension];
      const companyId = 'test-company-id';

      // Mock fetchHits 返回结果
      const mockFetchResponse = new HitDetailsBaseResponse();
      mockFetchResponse.Paging = {
        PageSize: 1,
        PageIndex: 1,
        TotalRecords: 2,
      };
      mockFetchResponse.Result = [
        { id: 'result1', companyName: '关联公司1' },
        { id: 'result2', companyName: '关联公司2' },
      ];

      jest.spyOn(processor, 'fetchHits').mockResolvedValue(mockFetchResponse);

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].key).toBe(DimensionTypeEnums.ActualControllerRiskChange);
      expect(result[0].isHit).toBe(true);
      expect(result[0].totalHits).toBe(2);
      expect(result[0].description).toContain('实际控制人风险变更');
    });

    it('应该正确处理命中记录条数不符合要求的情况', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RecentInvestCancellationsRiskChange;
      dimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [5], // 要求至少5条
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      }];

      const relatedRiskChangeDims = [dimension];
      const companyId = 'test-company-id';

      // Mock fetchHits 返回结果（只有2条，不满足要求）
      const mockFetchResponse = new HitDetailsBaseResponse();
      mockFetchResponse.Paging = {
        PageSize: 1,
        PageIndex: 1,
        TotalRecords: 2,
      };

      jest.spyOn(processor, 'fetchHits').mockResolvedValue(mockFetchResponse);

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].isHit).toBe(false);
      expect(result[0].totalHits).toBe(2);
    });
  });

  describe('fetchHits 方法测试', () => {
    it('应该正确处理实际控制人风险变更查询', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.ActualControllerRiskChange;
      dimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      }];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 获取实际控制人列表
      const mockActualControllers = [
        { keyNo: 'controller1', name: '控制人1' },
        { keyNo: 'controller2', name: '控制人2' },
      ];
      mockPersonHelper.getActualControllerList.mockResolvedValue(mockActualControllers);

      // Mock ES 查询结果
      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 3 },
            hits: [
              { _source: { Id: 'hit1', KeyNo: 'controller1', Category: 17 } },
              { _source: { Id: 'hit2', KeyNo: 'controller2', Category: 17 } },
              { _source: { Id: 'hit3', KeyNo: 'controller1', Category: 203 } },
            ],
          },
        },
      };
      mockSearchEs.mockResolvedValue(mockEsResponse);

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockPersonHelper.getActualControllerList).toHaveBeenCalledWith('test-company-id');
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          size: 10,
          from: 0,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { terms: { KeyNo: ['controller1', 'controller2'] } },
              ]),
            }),
          }),
        }),
        'test-company-id'
      );
      expect(result.Paging.TotalRecords).toBe(3);
      expect(result.Result).toHaveLength(3);
    });

    it('应该正确处理对外投资企业注销查询', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RecentInvestCancellationsRiskChange;
      dimension.dimensionHitStrategyFields = [
        {
          fieldKey: DimensionFieldKeyEnums.relatedRoleType,
          fieldValue: [RelatedTypeEnums.InvestCompany],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.timePeriod,
          fieldValue: [12],
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
      ];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 获取对外投资企业列表
      const mockInvestCompanies = [
        { keyNo: 'invest1', name: '投资公司1' },
        { keyNo: 'invest2', name: '投资公司2' },
      ];
      mockCompanySearchService.getInvestCompanyList.mockResolvedValue(mockInvestCompanies);

      // Mock ES 查询结果
      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 1 },
            hits: [
              { _source: { Id: 'hit1', KeyNo: 'invest1', Category: 38 } },
            ],
          },
        },
      };
      mockSearchEs.mockResolvedValue(mockEsResponse);

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockCompanySearchService.getInvestCompanyList).toHaveBeenCalledWith('test-company-id');
      expect(result.Paging.TotalRecords).toBe(1);
      expect(result.Result).toHaveLength(1);
    });

    it('应该正确处理上市主体风险变更查询', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.ListedEntityRiskChange;
      dimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ListedEntity],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      }];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 获取上市主体列表
      const mockListedEntities = [
        { keyNo: 'listed1', name: '上市公司1' },
      ];
      mockCompanySearchService.getListedEntityList.mockResolvedValue(mockListedEntities);

      // Mock ES 查询结果
      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 2 },
            hits: [
              { _source: { Id: 'hit1', KeyNo: 'listed1', Category: 1 } },
              { _source: { Id: 'hit2', KeyNo: 'listed1', Category: 2 } },
            ],
          },
        },
      };
      mockSearchEs.mockResolvedValue(mockEsResponse);

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(mockCompanySearchService.getListedEntityList).toHaveBeenCalledWith('test-company-id');
      expect(result.Paging.TotalRecords).toBe(2);
      expect(result.Result).toHaveLength(2);
    });

    it('应该正确处理没有关联企业的情况', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.ActualControllerRiskChange;
      dimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      }];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock 返回空的实际控制人列表
      mockPersonHelper.getActualControllerList.mockResolvedValue([]);

      // Act
      const result = await processor.fetchHits(dimension, params);

      // Assert
      expect(result.Paging.TotalRecords).toBe(0);
      expect(result.Result).toHaveLength(0);
      expect(mockSearchEs).not.toHaveBeenCalled();
    });
  });
});
