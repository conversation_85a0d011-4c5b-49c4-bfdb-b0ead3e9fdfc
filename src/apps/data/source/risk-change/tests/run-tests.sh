#!/bin/bash

# 风险动态模块测试运行脚本
# 
# 这个脚本提供了多种方式来运行风险动态模块的单元测试
# 包括完整测试、覆盖率报告、监听模式等

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印帮助信息
print_help() {
    echo "风险动态模块测试运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -a, --all               运行所有测试（默认）"
    echo "  -c, --coverage          运行测试并生成覆盖率报告"
    echo "  -w, --watch             监听模式运行测试"
    echo "  -f, --file <文件名>     运行指定的测试文件"
    echo "  -v, --verbose           详细输出模式"
    echo "  -s, --silent            静默模式"
    echo "  --core                  只运行核心服务测试"
    echo "  --processors            只运行处理器测试"
    echo "  --helpers               只运行辅助类测试"
    echo "  --clean                 清理测试缓存和覆盖率报告"
    echo ""
    echo "示例:"
    echo "  $0 -a                   # 运行所有测试"
    echo "  $0 -c                   # 运行测试并生成覆盖率报告"
    echo "  $0 -w                   # 监听模式"
    echo "  $0 -f base.helper       # 运行 base.helper 相关测试"
    echo "  $0 --core               # 只运行核心服务测试"
    echo "  $0 --clean              # 清理缓存"
}

# 获取项目根目录
get_project_root() {
    local current_dir=$(pwd)
    while [[ ! -f "$current_dir/package.json" ]]; do
        current_dir=$(dirname "$current_dir")
        if [[ "$current_dir" == "/" ]]; then
            print_message $RED "错误: 找不到项目根目录"
            exit 1
        fi
    done
    echo "$current_dir"
}

# 清理缓存和报告
clean_cache() {
    print_message $YELLOW "清理测试缓存和覆盖率报告..."
    
    local test_dir="$(dirname "$0")"
    
    # 删除 Jest 缓存
    if [[ -d "$test_dir/.jest-cache" ]]; then
        rm -rf "$test_dir/.jest-cache"
        print_message $GREEN "已删除 Jest 缓存"
    fi
    
    # 删除覆盖率报告
    if [[ -d "$test_dir/coverage" ]]; then
        rm -rf "$test_dir/coverage"
        print_message $GREEN "已删除覆盖率报告"
    fi
    
    print_message $GREEN "清理完成"
}

# 运行测试的基础函数
run_tests() {
    local test_pattern="$1"
    local extra_args="$2"
    
    local project_root=$(get_project_root)
    local test_dir="$(dirname "$0")"
    
    print_message $BLUE "开始运行测试..."
    print_message $YELLOW "测试目录: $test_dir"
    print_message $YELLOW "项目根目录: $project_root"
    
    cd "$project_root"
    
    # 构建 Jest 命令
    local jest_cmd="npx jest"
    jest_cmd="$jest_cmd --config=$test_dir/jest.config.js"
    
    if [[ -n "$test_pattern" ]]; then
        jest_cmd="$jest_cmd --testPathPattern=$test_pattern"
    fi
    
    if [[ -n "$extra_args" ]]; then
        jest_cmd="$jest_cmd $extra_args"
    fi
    
    print_message $BLUE "执行命令: $jest_cmd"
    
    # 执行测试
    if eval "$jest_cmd"; then
        print_message $GREEN "✅ 测试执行成功"
    else
        print_message $RED "❌ 测试执行失败"
        exit 1
    fi
}

# 解析命令行参数
COVERAGE=false
WATCH=false
VERBOSE=false
SILENT=false
TEST_FILE=""
TEST_PATTERN=""
EXTRA_ARGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            print_help
            exit 0
            ;;
        -a|--all)
            TEST_PATTERN=""
            shift
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -w|--watch)
            WATCH=true
            shift
            ;;
        -f|--file)
            TEST_FILE="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -s|--silent)
            SILENT=true
            shift
            ;;
        --core)
            TEST_PATTERN="risk-change-es.source"
            shift
            ;;
        --processors)
            TEST_PATTERN="processor"
            shift
            ;;
        --helpers)
            TEST_PATTERN="helper"
            shift
            ;;
        --clean)
            clean_cache
            exit 0
            ;;
        *)
            print_message $RED "未知选项: $1"
            print_help
            exit 1
            ;;
    esac
done

# 构建额外参数
if [[ "$COVERAGE" == true ]]; then
    EXTRA_ARGS="$EXTRA_ARGS --coverage"
fi

if [[ "$WATCH" == true ]]; then
    EXTRA_ARGS="$EXTRA_ARGS --watch"
fi

if [[ "$VERBOSE" == true ]]; then
    EXTRA_ARGS="$EXTRA_ARGS --verbose"
fi

if [[ "$SILENT" == true ]]; then
    EXTRA_ARGS="$EXTRA_ARGS --silent"
fi

# 处理特定文件测试
if [[ -n "$TEST_FILE" ]]; then
    TEST_PATTERN="$TEST_FILE"
fi

# 显示测试配置
print_message $BLUE "=== 风险动态模块测试配置 ==="
print_message $YELLOW "测试模式: $(if [[ -n "$TEST_PATTERN" ]]; then echo "指定模式 ($TEST_PATTERN)"; else echo "全部测试"; fi)"
print_message $YELLOW "覆盖率报告: $(if [[ "$COVERAGE" == true ]]; then echo "启用"; else echo "禁用"; fi)"
print_message $YELLOW "监听模式: $(if [[ "$WATCH" == true ]]; then echo "启用"; else echo "禁用"; fi)"
print_message $YELLOW "详细输出: $(if [[ "$VERBOSE" == true ]]; then echo "启用"; else echo "禁用"; fi)"
print_message $BLUE "================================"

# 运行测试
run_tests "$TEST_PATTERN" "$EXTRA_ARGS"

# 如果启用了覆盖率，显示报告位置
if [[ "$COVERAGE" == true ]]; then
    local test_dir="$(dirname "$0")"
    local coverage_dir="$test_dir/coverage"
    
    if [[ -d "$coverage_dir" ]]; then
        print_message $GREEN "📊 覆盖率报告已生成:"
        print_message $YELLOW "  HTML 报告: $coverage_dir/lcov-report/index.html"
        print_message $YELLOW "  LCOV 文件: $coverage_dir/lcov.info"
        print_message $YELLOW "  JSON 报告: $coverage_dir/coverage-final.json"
    fi
fi

print_message $GREEN "🎉 测试运行完成!"
