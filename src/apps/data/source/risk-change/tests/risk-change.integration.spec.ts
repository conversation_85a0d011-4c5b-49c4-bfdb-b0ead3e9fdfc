import { Test, TestingModule } from '@nestjs/testing';
import { RiskChangeEsSource } from '../risk-change-es.source';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { RiskChangeHelperModule } from '../risk-change.helper.module';
import { ConfigService } from 'libs/config/config.service';
import { CompanySearchService } from '../../../company/company-search.service';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

/**
 * 风险动态模块集成测试
 * 
 * 这个测试文件验证各个组件之间的协作是否正常
 * 使用真实的依赖关系，但模拟外部服务（如 ES、数据库等）
 */
describe('风险动态模块集成测试', () => {
  let riskChangeService: RiskChangeEsSource;
  let dimensionHitDetailProcessor: DimensionHitDetailProcessor;
  let relatedDimensionHitDetailsProcessor: RelatedDimensionHitDetailProcessor;
  let module: TestingModule;

  const [testOrgId, testUserId] = generateUniqueTestIds('risk-change.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeAll(async () => {
    // 创建测试模块，使用真实的依赖关系
    module = await Test.createTestingModule({
      imports: [RiskChangeHelperModule],
      providers: [
        RiskChangeEsSource,
        {
          provide: ConfigService,
          useValue: {
            esConfig: {
              riskChangeList: {
                nodes: ['http://localhost:9200'],
                indexName: 'test-risk-change-index',
              },
            },
          },
        },
        {
          provide: CompanySearchService,
          useValue: {
            getInvestCompanyList: jest.fn().mockResolvedValue([
              { keyNo: 'invest1', name: '投资公司1' },
              { keyNo: 'invest2', name: '投资公司2' },
            ]),
            getListedEntityList: jest.fn().mockResolvedValue([
              { keyNo: 'listed1', name: '上市公司1' },
            ]),
          },
        },
        {
          provide: RiskChangeHelper,
          useValue: {
            hitMainInfoUpdateCapitalChange: jest.fn().mockReturnValue([
              { id: 'capital1', Category: 37 },
            ]),
          },
        },
      ],
    }).compile();

    riskChangeService = module.get<RiskChangeEsSource>(RiskChangeEsSource);
    dimensionHitDetailProcessor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    relatedDimensionHitDetailsProcessor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);

    // 模拟 ES 搜索函数
    const mockEsSearch = jest.fn().mockResolvedValue({
      body: {
        hits: {
          total: { value: 2 },
          hits: [
            {
              _source: {
                Id: 'risk1',
                KeyNo: 'test-company-id',
                Category: 1,
                ChangeDate: Date.now(),
                ChangeExtend: JSON.stringify({
                  A: '张三',
                  B: '李四',
                }),
              },
            },
            {
              _source: {
                Id: 'risk2',
                KeyNo: 'test-company-id',
                Category: 37,
                ChangeDate: Date.now(),
                ChangeExtend: JSON.stringify({
                  amount: 1000000,
                }),
              },
            },
          ],
        },
      },
    });

    // 绑定 ES 搜索函数到处理器
    dimensionHitDetailProcessor.bindRiskChangeEsSearchFn(mockEsSearch);
    relatedDimensionHitDetailsProcessor.bindRiskChangeEsSearchFn(mockEsSearch);

    // 手动设置 ES 客户端
    (riskChangeService as any).esClient = {
      search: mockEsSearch,
    };
  });

  afterAll(async () => {
    await module.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('完整的风险分析流程测试', () => {
    it('应该正确处理混合维度的分析请求', async () => {
      // Arrange - 创建包含关联方和非关联方维度的请求
      const companyId = 'test-company-id-12345678901234567890';
      
      const normalDimension = new DimensionHitStrategyPO();
      normalDimension.key = DimensionTypeEnums.RiskChange;
      normalDimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.Equal,
      }];

      const relatedDimension = new DimensionHitStrategyPO();
      relatedDimension.key = DimensionTypeEnums.ActualControllerRiskChange;
      relatedDimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [1],
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      }];

      const dimensions = [normalDimension, relatedDimension];

      // Mock 父类的 analyzeByAggs 方法
      jest.spyOn(riskChangeService as any, 'analyzeByAggs').mockResolvedValue([{
        key: DimensionTypeEnums.RiskChange,
        isHit: true,
        totalHits: 2,
        description: '风险动态命中',
      }]);

      // Act
      const result = await riskChangeService.analyze(companyId, dimensions);

      // Assert
      expect(result).toHaveLength(2);
      
      // 验证普通维度结果
      const normalResult = result.find(r => r.key === DimensionTypeEnums.RiskChange);
      expect(normalResult).toBeDefined();
      expect(normalResult?.isHit).toBe(true);
      expect(normalResult?.totalHits).toBe(2);

      // 验证关联方维度结果
      const relatedResult = result.find(r => r.key === DimensionTypeEnums.ActualControllerRiskChange);
      expect(relatedResult).toBeDefined();
      expect(typeof relatedResult?.isHit).toBe('boolean');
    });

    it('应该正确处理详情查询的完整流程', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      dimension.dimensionHitStrategyFields = [];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id-12345678901234567890';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Act
      const result = await riskChangeService.getDimensionDetail(dimension, params);

      // Assert
      expect(result).toBeDefined();
      expect(result.Paging).toBeDefined();
      expect(result.Paging.PageSize).toBe(10);
      expect(result.Paging.PageIndex).toBe(1);
      expect(result.Result).toBeDefined();
      expect(Array.isArray(result.Result)).toBe(true);
    });

    it('应该正确处理资本变更维度的特殊逻辑', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.MainInfoUpdateCapitalChange;

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id-12345678901234567890';
      params.pageIndex = 1;
      params.pageSize = 10;

      // Mock getDetailFromEs 方法
      jest.spyOn(riskChangeService as any, 'getDetailFromEs').mockResolvedValue({
        Result: [
          { Id: 'capital1', Category: 37 },
          { Id: 'capital2', Category: 37 },
          { Id: 'capital3', Category: 37 },
        ],
        Paging: {
          PageSize: 10,
          PageIndex: 1,
          TotalRecords: 3,
        },
      });

      // Act
      const result = await riskChangeService.getDimensionDetail(dimension, params);

      // Assert
      expect(result).toBeDefined();
      expect(result.Result).toHaveLength(1); // RiskChangeHelper 过滤后只返回1条
      expect(result.Paging.TotalRecords).toBe(1);
    });
  });

  describe('错误处理和边界情况测试', () => {
    it('应该正确处理 ES 查询失败的情况', async () => {
      // Arrange
      const companyId = 'test-company-id-12345678901234567890';
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;

      // Mock ES 查询失败
      const mockEsError = jest.fn().mockRejectedValue(new Error('ES connection failed'));
      (riskChangeService as any).esClient.search = mockEsError;

      // Act & Assert
      await expect(
        riskChangeService.getDimensionDetail(dimension, {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        } as HitDetailsBaseQueryParams)
      ).rejects.toThrow('ES connection failed');
    });

    it('应该正确处理空的查询结果', async () => {
      // Arrange
      const companyId = 'test-company-id-12345678901234567890';
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;

      // Mock 空的 ES 响应
      const mockEmptyResponse = jest.fn().mockResolvedValue({
        body: {
          hits: {
            total: { value: 0 },
            hits: [],
          },
        },
      });
      (riskChangeService as any).esClient.search = mockEmptyResponse;

      // Act
      const result = await riskChangeService.getDimensionDetail(dimension, {
        keyNo: companyId,
        pageIndex: 1,
        pageSize: 10,
      } as HitDetailsBaseQueryParams);

      // Assert
      expect(result).toBeDefined();
      expect(result.Result).toHaveLength(0);
      expect(result.Paging.TotalRecords).toBe(0);
    });

    it('应该正确处理无效的维度类型', async () => {
      // Arrange
      const companyId = 'test-company-id-12345678901234567890';
      const dimension = new DimensionHitStrategyPO();
      dimension.key = 'InvalidDimensionType' as any;

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = companyId;
      params.pageIndex = 1;
      params.pageSize = 10;

      // Act
      const result = await riskChangeService.getDimensionDetail(dimension, params);

      // Assert
      // 应该回退到父类的默认处理逻辑
      expect(result).toBeDefined();
    });
  });

  describe('性能和并发测试', () => {
    it('应该能够处理并发的分析请求', async () => {
      // Arrange
      const companyId = 'test-company-id-12345678901234567890';
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;

      // Mock 父类方法
      jest.spyOn(riskChangeService as any, 'analyzeByAggs').mockResolvedValue([{
        key: DimensionTypeEnums.RiskChange,
        isHit: true,
        totalHits: 1,
        description: '并发测试',
      }]);

      // Act - 并发执行多个分析请求
      const promises = Array.from({ length: 5 }, () =>
        riskChangeService.analyze(companyId, [dimension])
      );

      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result).toHaveLength(1);
        expect(result[0].key).toBe(DimensionTypeEnums.RiskChange);
        expect(result[0].isHit).toBe(true);
      });
    });

    it('应该能够处理大量的详情数据', async () => {
      // Arrange
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id-12345678901234567890';
      params.pageIndex = 1;
      params.pageSize = 1000; // 大页面大小

      // Mock 大量数据的 ES 响应
      const largeDataSet = Array.from({ length: 1000 }, (_, index) => ({
        _source: {
          Id: `risk-${index}`,
          KeyNo: 'test-company-id',
          Category: 1,
          ChangeDate: Date.now(),
          ChangeExtend: JSON.stringify({ index }),
        },
      }));

      const mockLargeResponse = jest.fn().mockResolvedValue({
        body: {
          hits: {
            total: { value: 1000 },
            hits: largeDataSet,
          },
        },
      });
      (riskChangeService as any).esClient.search = mockLargeResponse;

      // Act
      const startTime = Date.now();
      const result = await riskChangeService.getDimensionDetail(dimension, params);
      const endTime = Date.now();

      // Assert
      expect(result).toBeDefined();
      expect(result.Result).toHaveLength(1000);
      expect(result.Paging.TotalRecords).toBe(1000);
      
      // 性能断言 - 处理1000条记录应该在合理时间内完成（比如5秒）
      expect(endTime - startTime).toBeLessThan(5000);
    });
  });
});
