import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { PersonHelper } from '../../helper/person.helper';
import { BaseHelper } from '../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('DimensionHitDetailProcessor 单元测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  const [testOrgId, testUserId] = generateUniqueTestIds('dimension-hit-detail.processor.unittest.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeEach(async () => {
    // 创建 mock 对象
    mockRiskChangeHelper = {
      hitMainInfoUpdateCapitalChange: jest.fn(),
    } as any;

    mockPersonHelper = {
      getPersonData: jest.fn(),
    } as any;

    mockBaseHelper = {
      restricterTypeField: jest.fn(),
      amountField: jest.fn(),
      category28Field: jest.fn(),
      filterLastYearData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchHits 方法测试', () => {
    it('应该正确处理空的详情响应', async () => {
      // Arrange
      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [];
      
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toEqual([]);
    });

    it('应该正确处理法定代表人变更类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-id',
        Category: RiskChangeCategoryEnum.category1, // 法定代表人变更
        ChangeExtend: JSON.stringify({
          A: '张三',
          B: '李四',
        }),
        Extend1: JSON.stringify({
          someData: 'test',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];
      
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      dimension.dimensionHitStrategyFields = [];
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-id');
      expect(result[0].ChangeExtend).toEqual({
        A: '张三',
        B: '李四',
      });
      expect(result[0].Extend1).toEqual({
        someData: 'test',
      });
    });

    it('应该正确处理股东股份变更类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-id',
        Category: RiskChangeCategoryEnum.category44, // 股东股比变更
        ChangeExtend: JSON.stringify({
          A: '股东变更信息',
          B: '变更前',
          C: '变更后',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];
      
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      dimension.dimensionHitStrategyFields = [];
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Category).toBe(RiskChangeCategoryEnum.category44);
    });

    it('应该正确处理带有字段过滤条件的维度', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-id',
        Category: RiskChangeCategoryEnum.category37, // 注册资本
        ChangeExtend: JSON.stringify({
          amount: 1000000,
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];
      
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      dimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.amount,
        fieldValue: [100], // 100万以上
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      }];
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock baseHelper.amountField 返回命中
      mockBaseHelper.amountField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(mockBaseHelper.amountField).toHaveBeenCalled();
      expect(result).toHaveLength(1);
    });

    it('应该正确过滤不符合条件的记录', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-id',
        Category: RiskChangeCategoryEnum.category37, // 注册资本
        ChangeExtend: JSON.stringify({
          amount: 50000, // 5万，不符合条件
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];
      
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      dimension.dimensionHitStrategyFields = [{
        fieldKey: DimensionFieldKeyEnums.amount,
        fieldValue: [100], // 100万以上
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      }];
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock baseHelper.amountField 返回不命中
      mockBaseHelper.amountField.mockReturnValue(false);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(mockBaseHelper.amountField).toHaveBeenCalled();
      expect(result).toHaveLength(0);
    });

    it('应该正确处理 JSON 解析错误', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-id',
        Category: RiskChangeCategoryEnum.category1,
        ChangeExtend: 'invalid-json', // 无效的 JSON
        Extend1: '{"valid": "json"}',
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];
      
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.RiskChange;
      dimension.dimensionHitStrategyFields = [];
      
      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].ChangeExtend).toBe('invalid-json'); // 保持原值
      expect(result[0].Extend1).toEqual({ valid: 'json' }); // 正确解析
    });
  });

  describe('commonCivilRiskChange 方法测试', () => {
    it('应该正确构建 ES 查询并返回结果', async () => {
      // Arrange
      const companyIds = ['company1', 'company2'];
      const categories = [RiskChangeCategoryEnum.category1, RiskChangeCategoryEnum.category2];
      const timePeriod = 12;
      const timeUnit = 'month';
      const pageSize = 100;

      const mockEsResponse = {
        body: {
          hits: {
            total: { value: 2 },
            hits: [
              { _source: { Id: 'hit1', Category: 1 } },
              { _source: { Id: 'hit2', Category: 2 } },
            ],
          },
        },
      };

      mockSearchEs.mockResolvedValue(mockEsResponse);

      // Act
      const result = await processor.commonCivilRiskChange(
        companyIds,
        categories,
        timePeriod,
        timeUnit,
        pageSize
      );

      // Assert
      expect(mockSearchEs).toHaveBeenCalledWith(
        expect.objectContaining({
          size: pageSize,
          query: expect.objectContaining({
            bool: expect.objectContaining({
              filter: expect.arrayContaining([
                { terms: { KeyNo: companyIds } },
                { terms: { Category: categories } },
                expect.objectContaining({
                  range: expect.objectContaining({
                    ChangeDate: expect.any(Object),
                  }),
                }),
              ]),
            }),
          }),
        }),
        companyIds[0]
      );

      expect(result.Paging.TotalRecords).toBe(2);
      expect(result.Result).toHaveLength(2);
      expect(result.Result[0].Id).toBe('hit1');
      expect(result.Result[1].Id).toBe('hit2');
    });

    it('应该正确处理 ES 查询错误', async () => {
      // Arrange
      const companyIds = ['company1'];
      const categories = [RiskChangeCategoryEnum.category1];
      const timePeriod = 12;
      const timeUnit = 'month';
      const pageSize = 100;

      const mockError = new Error('ES connection failed');
      mockSearchEs.mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        processor.commonCivilRiskChange(companyIds, categories, timePeriod, timeUnit, pageSize)
      ).rejects.toThrow('ES connection failed');
    });
  });
});
