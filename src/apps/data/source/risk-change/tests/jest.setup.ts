/**
 * Jest 测试环境设置文件
 * 
 * 这个文件在每个测试文件运行前执行，用于设置全局的测试环境
 * 包括自定义匹配器、全局 mock、环境变量等
 */

import 'reflect-metadata';

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.TZ = 'UTC';

// 全局测试超时时间
jest.setTimeout(30000);

// 全局 mock console 方法（可选，用于减少测试输出噪音）
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  // 可以选择性地静默某些 console 输出
  console.error = (...args: any[]) => {
    // 过滤掉一些已知的无害错误信息
    const message = args[0];
    if (typeof message === 'string') {
      // 例如：过滤掉某些库的警告信息
      if (message.includes('Warning: ')) {
        return;
      }
    }
    originalConsoleError(...args);
  };

  console.warn = (...args: any[]) => {
    // 过滤掉一些已知的无害警告信息
    const message = args[0];
    if (typeof message === 'string') {
      if (message.includes('DeprecationWarning')) {
        return;
      }
    }
    originalConsoleWarn(...args);
  };
});

afterAll(() => {
  // 恢复原始的 console 方法
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// 全局的测试工具函数
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidDate(): R;
      toBeValidCompanyId(): R;
      toHaveValidPagination(): R;
    }
  }
}

// 自定义匹配器
expect.extend({
  // 验证是否为有效日期
  toBeValidDate(received: any) {
    const pass = received instanceof Date && !isNaN(received.getTime());
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid date`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid date`,
        pass: false,
      };
    }
  },

  // 验证是否为有效的公司ID
  toBeValidCompanyId(received: any) {
    const pass = typeof received === 'string' && received.length === 32;
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid company ID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid company ID (32-character string)`,
        pass: false,
      };
    }
  },

  // 验证分页对象是否有效
  toHaveValidPagination(received: any) {
    const hasRequiredFields = 
      received &&
      typeof received.PageSize === 'number' &&
      typeof received.PageIndex === 'number' &&
      typeof received.TotalRecords === 'number' &&
      received.PageSize > 0 &&
      received.PageIndex > 0 &&
      received.TotalRecords >= 0;

    if (hasRequiredFields) {
      return {
        message: () => `expected pagination object to be invalid`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected pagination object to have valid PageSize, PageIndex, and TotalRecords`,
        pass: false,
      };
    }
  },
});

// 全局的 mock 设置
beforeEach(() => {
  // 清除所有 mock 的调用历史
  jest.clearAllMocks();
  
  // 重置所有 mock 的实现
  jest.resetAllMocks();
});

// 全局的测试数据工厂函数
export const TestDataFactory = {
  // 创建测试用的公司ID
  createCompanyId: (suffix = ''): string => {
    const base = 'test-company-id-' + suffix;
    return base.padEnd(32, '0').substring(0, 32);
  },

  // 创建测试用的维度策略
  createDimensionStrategy: (overrides = {}): any => {
    return {
      key: 'RiskChange',
      dimensionHitStrategyFields: [],
      dimensionFilter: {},
      ...overrides,
    };
  },

  // 创建测试用的分页参数
  createPaginationParams: (overrides = {}): any => {
    return {
      keyNo: TestDataFactory.createCompanyId(),
      pageIndex: 1,
      pageSize: 10,
      ...overrides,
    };
  },

  // 创建测试用的ES响应
  createEsResponse: (hits = [], total = 0): any => {
    return {
      body: {
        hits: {
          total: { value: total },
          hits: hits.map(hit => ({ _source: hit })),
        },
      },
    };
  },

  // 创建测试用的风险动态项
  createRiskChangeItem: (overrides = {}): any => {
    return {
      Id: 'test-risk-id',
      KeyNo: TestDataFactory.createCompanyId(),
      Category: 1,
      ChangeDate: Date.now(),
      ChangeExtend: JSON.stringify({}),
      Extend1: JSON.stringify({}),
      ...overrides,
    };
  },
};

// 导出常用的测试常量
export const TEST_CONSTANTS = {
  DEFAULT_COMPANY_ID: TestDataFactory.createCompanyId('default'),
  DEFAULT_PAGE_SIZE: 10,
  DEFAULT_PAGE_INDEX: 1,
  TEST_TIMEOUT: 30000,
};

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// 设置默认的时区
if (!process.env.TZ) {
  process.env.TZ = 'UTC';
}
