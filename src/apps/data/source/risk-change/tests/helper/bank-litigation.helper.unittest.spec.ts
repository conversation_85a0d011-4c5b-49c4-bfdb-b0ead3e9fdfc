import { Test, TestingModule } from '@nestjs/testing';
import { BankLitigationHelper } from '../../helper/bank-litigation.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('BankLitigationHelper 单元测试', () => {
  let helper: BankLitigationHelper;

  const [testOrgId, testUserId] = generateUniqueTestIds('bank-litigation.helper.unittest.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BankLitigationHelper],
    }).compile();

    helper = module.get<BankLitigationHelper>(BankLitigationHelper);
  });

  describe('checkBankOrFinancialLeasingField49 方法测试', () => {
    it('应该正确识别银行作为原告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1]; // 期望有银行或金融租赁作为原告
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [
            {
              Role: '原告',
              Name: '中国工商银行股份有限公司',
            },
            {
              Role: '被告',
              Name: '某某公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField49(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别金融租赁公司作为原告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [
            {
              Role: '原告',
              Name: '某某金融租赁有限公司',
            },
            {
              Role: '被告',
              Name: '某某公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField49(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理非银行非金融租赁作为原告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [
            {
              Role: '原告',
              Name: '某某贸易有限公司',
            },
            {
              Role: '被告',
              Name: '某某公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField49(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理银行作为被告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [
            {
              Role: '原告',
              Name: '某某公司',
            },
            {
              Role: '被告',
              Name: '中国建设银行股份有限公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField49(field, item);

      // Assert
      expect(result).toBe(false); // 银行作为被告不符合条件
    });

    it('应该正确处理空的当事人列表', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField49(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理期望值为0的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [0]; // 期望没有银行或金融租赁作为原告
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [
            {
              Role: '原告',
              Name: '某某贸易有限公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField49(field, item);

      // Assert
      expect(result).toBe(true);
    });
  });

  describe('checkBankOrFinancialLeasingField4 方法测试', () => {
    it('应该正确识别银行作为原告的情况（字段K）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          K: [
            {
              RN: '原告',
              Name: '中国农业银行股份有限公司',
            },
            {
              RN: '被告',
              Name: '某某公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField4(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理字段K为空的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          K: [],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField4(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('checkBankOrFinancialLeasingField18 方法测试', () => {
    it('应该正确识别开庭公告中银行作为原告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          D: [
            {
              RN: '原告',
              P: '中国银行股份有限公司深圳分行',
            },
            {
              RN: '被告',
              P: '某某公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField18(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别开庭公告中金融租赁公司作为原告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          D: [
            {
              RN: '原告',
              P: '某某金融租赁股份有限公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField18(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理开庭公告中非银行非金融租赁作为原告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          D: [
            {
              RN: '原告',
              P: '某某科技有限公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField18(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('checkBankOrFinancialLeasingField90 方法测试', () => {
    it('应该正确识别诉前调解中银行作为原告的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [
            {
              Role: '原告',
              Name: '招商银行股份有限公司',
            },
            {
              Role: '被告',
              Name: '某某公司',
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField90(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理诉前调解中空的当事人列表', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField90(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理 ChangeExtend 为空的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {},
      };

      // Act
      const result49 = helper.checkBankOrFinancialLeasingField49(field, item);
      const result4 = helper.checkBankOrFinancialLeasingField4(field, item);
      const result18 = helper.checkBankOrFinancialLeasingField18(field, item);
      const result90 = helper.checkBankOrFinancialLeasingField90(field, item);

      // Assert
      expect(result49).toBe(false);
      expect(result4).toBe(false);
      expect(result18).toBe(false);
      expect(result90).toBe(false);
    });

    it('应该正确处理包含银行和金融租赁关键词的复合名称', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;

      const item = {
        ChangeExtend: {
          I: [
            {
              Role: '原告',
              Name: '某某银行金融租赁有限公司', // 同时包含银行和金融租赁
            },
          ],
        },
      };

      // Act
      const result = helper.checkBankOrFinancialLeasingField49(field, item);

      // Assert
      expect(result).toBe(true);
    });
  });
});
