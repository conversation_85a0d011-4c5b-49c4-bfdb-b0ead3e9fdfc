import { Test, TestingModule } from '@nestjs/testing';
import { CaseReasonHelper } from '../../helper/case-reason.helper';
import { CompanyDetailService } from '../../../../company/company-detail.service';
import { MongodbSearchHelper } from '../../../helper/mongodb.search.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('CaseReasonHelper 单元测试', () => {
  let helper: CaseReasonHelper;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockMongodbHelper: jest.Mocked<MongodbSearchHelper>;

  const [testOrgId, testUserId] = generateUniqueTestIds('case-reason.helper.unittest.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeEach(async () => {
    mockCompanyDetailService = {
      getCompanyDetail: jest.fn(),
    } as any;

    mockMongodbHelper = {
      searchDocuments: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CaseReasonHelper,
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: MongodbSearchHelper, useValue: mockMongodbHelper },
      ],
    }).compile();

    helper = module.get<CaseReasonHelper>(CaseReasonHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('checkCaseTypeField 方法测试', () => {
    it('应该正确识别刑事案由（A开头）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A']; // 刑事案由
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'A0101', // 刑事案由代码
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别民事案由（B开头）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['B']; // 民事案由
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'B0201', // 民事案由代码
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别执行案由（C开头）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['C']; // 执行案由
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'C0301', // 执行案由代码
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别行政案由（D开头）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['D']; // 行政案由
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'D0401', // 行政案由代码
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确识别其他案由（E开头）', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['E']; // 其他案由
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'E0501', // 其他案由代码
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理多种案由类型', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A', 'B']; // 刑事和民事案由
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'B0201', // 民事案由代码
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的案由类型', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A']; // 只要刑事案由
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'B0201', // 但实际是民事案由
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的案由代码', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: null,
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少案由代码字段的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {},
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('caseReasonTypeFieldByKeyCauseAction 方法测试', () => {
    it('应该正确识别关键案由', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      // 假设 keyCauseActionMap 中包含 'B0201' 作为关键案由
      const item = {
        ChangeExtend: {
          RC: 'B0201', // 假设这是一个关键案由
        },
      };

      // Act
      const result = helper.caseReasonTypeFieldByKeyCauseAction(field, item);

      // Assert
      // 这里需要根据实际的 keyCauseActionMap 来判断
      // 假设 B0201 在关键案由列表中
      expect(typeof result).toBe('boolean');
    });

    it('应该正确处理非关键案由', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'X9999', // 假设这不是关键案由
        },
      };

      // Act
      const result = helper.caseReasonTypeFieldByKeyCauseAction(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的案由代码', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: null,
        },
      };

      // Act
      const result = helper.caseReasonTypeFieldByKeyCauseAction(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('caseReasonTypeField 方法测试', () => {
    it('应该在fieldValue为空时调用关键案由方法', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = []; // 空的fieldValue
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'B0201',
        },
      };

      // Spy on the key cause action method
      const spy = jest.spyOn(helper, 'caseReasonTypeFieldByKeyCauseAction');
      spy.mockReturnValue(true);

      // Act
      const result = helper.caseReasonTypeField(field, item);

      // Assert
      expect(spy).toHaveBeenCalledWith(field, item);
      expect(result).toBe(true);

      spy.mockRestore();
    });

    it('应该正确匹配案由前缀', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['B02']; // 案由前缀
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'B0201', // 匹配前缀
        },
      };

      // Act
      const result = helper.caseReasonTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理不匹配的案由前缀', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A01']; // 案由前缀
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'B0201', // 不匹配前缀
        },
      };

      // Act
      const result = helper.caseReasonTypeField(field, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理多个案由前缀', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A01', 'B02', 'C03']; // 多个案由前缀
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: 'B0201', // 匹配其中一个前缀
        },
      };

      // Act
      const result = helper.caseReasonTypeField(field, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理空的案由代码', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['B02'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: null,
        },
      };

      // Act
      const result = helper.caseReasonTypeField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理 ChangeExtend 为空的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {},
      };

      // Act
      const result1 = helper.checkCaseTypeField(field, item);
      const result2 = helper.caseReasonTypeField(field, item);

      // Assert
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });

    it('应该正确处理案由代码为空字符串的情况', () => {
      // Arrange
      const field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['A'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          RC: '',
        },
      };

      // Act
      const result = helper.checkCaseTypeField(field, item);

      // Assert
      expect(result).toBe(false);
    });
  });
});
