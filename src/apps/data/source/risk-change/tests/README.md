# 风险动态模块单元测试

本目录包含了风险动态模块的完整单元测试套件，用于确保代码质量和功能正确性。

## 测试结构

```
tests/
├── README.md                                          # 测试说明文档
├── test-suite.spec.ts                                # 测试套件入口
├── risk-change-es.source.unittest.spec.ts           # 核心服务测试
├── dimension-hit-detail.processor.unittest.spec.ts   # 非关联方处理器测试
├── related-dimension-hit-detail.processor.unittest.spec.ts # 关联方处理器测试
└── helper/                                           # 辅助类测试
    ├── base.helper.unittest.spec.ts                 # 基础辅助类测试
    ├── bank-litigation.helper.unittest.spec.ts      # 银行诉讼辅助类测试
    └── case-reason.helper.unittest.spec.ts          # 案由辅助类测试
```

## 运行测试

### 运行所有测试
```bash
npm test -- src/apps/data/source/risk-change/tests
```

### 运行特定测试文件
```bash
# 运行核心服务测试
npm test -- src/apps/data/source/risk-change/tests/risk-change-es.source.unittest.spec.ts

# 运行处理器测试
npm test -- src/apps/data/source/risk-change/tests/dimension-hit-detail.processor.unittest.spec.ts

# 运行辅助类测试
npm test -- src/apps/data/source/risk-change/tests/helper/base.helper.unittest.spec.ts
```

### 运行测试并生成覆盖率报告
```bash
npm test -- --coverage src/apps/data/source/risk-change/tests
```

### 监听模式运行测试
```bash
npm test -- --watch src/apps/data/source/risk-change/tests
```

## 测试覆盖范围

### 1. RiskChangeEsSource (核心服务)
- ✅ analyze 方法 - 维度分析逻辑
- ✅ getDimensionDetail 方法 - 详情查询逻辑
- ✅ 关联方和非关联方维度的分离处理
- ✅ 错误处理和边界情况

### 2. DimensionHitDetailProcessor (非关联方处理器)
- ✅ fetchHits 方法 - 详情分析和过滤
- ✅ commonCivilRiskChange 方法 - ES 查询构建
- ✅ 不同风险类型的处理逻辑
- ✅ JSON 解析错误处理

### 3. RelatedDimensionHitDetailProcessor (关联方处理器)
- ✅ processAnalyze 方法 - 关联方分析流程
- ✅ fetchHits 方法 - 关联方查询逻辑
- ✅ 实际控制人、对外投资、上市主体的处理
- ✅ 命中记录条数判断

### 4. BaseHelper (基础辅助类)
- ✅ restricterTypeField - 限制高消费对象识别
- ✅ amountField - 金额范围判断
- ✅ category28Field - 融资动态过滤
- ✅ filterLastYearData - 年度数据过滤

### 5. BankLitigationHelper (银行诉讼辅助类)
- ✅ checkBankOrFinancialLeasingField49 - 裁判文书银行识别
- ✅ checkBankOrFinancialLeasingField4 - 字段K银行识别
- ✅ checkBankOrFinancialLeasingField18 - 开庭公告银行识别
- ✅ checkBankOrFinancialLeasingField90 - 诉前调解银行识别

### 6. CaseReasonHelper (案由辅助类)
- ✅ checkCaseTypeField - 案件类型识别
- ✅ caseReasonTypeField - 案由匹配逻辑
- ✅ caseReasonTypeFieldByKeyCauseAction - 关键案由处理

## 测试数据管理

### 测试用户生成
所有测试都使用统一的测试用户生成机制：

```typescript
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

const [testOrgId, testUserId] = generateUniqueTestIds('your.test.file.name.ts');
const testUser = getTestUser(testOrgId, testUserId);
```

### 数据清理
- 测试后自动清理生成的测试数据
- 使用 testOrgId 和 testUserId 确保数据隔离
- 避免测试间的数据污染

## Mock 策略

### 外部依赖 Mock
- ES 客户端：模拟 Elasticsearch 查询和响应
- 数据库服务：模拟公司详情、人员信息等查询
- 配置服务：提供测试环境配置

### Mock 对象示例
```typescript
const mockEsClient = {
  search: jest.fn().mockResolvedValue({
    body: {
      hits: {
        total: { value: 1 },
        hits: [{ _source: { id: 'test' } }]
      }
    }
  })
};
```

## 测试最佳实践

### 1. 测试命名
- 使用中文描述测试意图
- 遵循 "应该 + 预期行为" 的格式
- 例如：`应该正确处理空的维度策略列表`

### 2. 测试结构
```typescript
describe('类名 单元测试', () => {
  describe('方法名 测试', () => {
    it('应该正确处理正常情况', () => {
      // Arrange - 准备测试数据
      // Act - 执行被测试的方法
      // Assert - 验证结果
    });
  });
});
```

### 3. 断言策略
- 验证返回值的正确性
- 验证 Mock 方法的调用次数和参数
- 验证异常情况的处理

### 4. 边界情况测试
- 空值、null、undefined 的处理
- 异常输入的处理
- 边界值的测试

## 持续集成

### 测试要求
- 所有测试必须通过才能合并代码
- 代码覆盖率要求：
  - 行覆盖率 ≥ 85%
  - 分支覆盖率 ≥ 80%
  - 函数覆盖率 ≥ 90%

### 测试报告
- 自动生成测试覆盖率报告
- 在 CI 流程中展示测试结果
- 失败测试的详细错误信息

## 扩展测试

如需添加新的测试：

1. 在对应目录创建 `*.unittest.spec.ts` 文件
2. 遵循现有的测试结构和命名规范
3. 确保新测试覆盖所有公共方法
4. 更新本 README 文档

## 故障排除

### 常见问题
1. **Mock 对象未正确设置**：检查 Mock 方法的返回值和调用参数
2. **测试数据冲突**：确保使用唯一的测试 ID
3. **异步测试失败**：检查 async/await 的使用

### 调试技巧
- 使用 `console.log` 输出中间结果
- 使用 Jest 的 `--verbose` 选项查看详细输出
- 单独运行失败的测试用例进行调试
